<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.lxwz.lxwzdata.Mapper.LxwzjctsZbHistoryMapper">
<!--    批量插入数据  -->
    <insert id="batchInsert" parameterType="java.util.List">
        INSERT INTO lxwzjcts_zb_history (
        result, jsonResult, createdTime,
        searchType, searchVal, zbName, zbFz, zbFm
        ) VALUES
        <foreach collection="list" item="item" separator=",">
            (
            #{item.result},
            #{item.jsonResult},
            NOW(),  <!-- 静态值直接写函数 -->
            #{item.searchtype},   <!-- 注意字段名驼峰转下划线 -->
            #{item.searchVal},    <!-- 同上 -->
            #{item.zbName},
            #{item.zbFz},
            #{item.zbFm}
            )
        </foreach>
    </insert>
    <!-- 插入数据（自动填充 created_time） -->
    <insert id="insert" parameterType="LxwzjctsZbHistory" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO lxwzjcts_zb_history (result, jsonResult, createdTime,searchType, searchVal, zbName,zbFz,zbFm)
        VALUES (#{result}, #{jsonResult}, NOW(),#{searchType} ,#{searchVal},#{zbName},#{zbFz},#{zbFm})
    </insert>

    <!-- 根据 ID 查询 -->
    <select id="selectById" parameterType="Long" resultType="LxwzjctsZbHistory">
        SELECT
            id,
            result,
            jsonResult AS jsonResult,
            createdTime AS createdTime
        FROM lxwzjcts_zb_history
        WHERE id = #{id}
    </select>

    <select id="selectByCondition" resultType="LxwzjctsZbHistory">
        SELECT *
        FROM lxwzjcts_zb_history
        <where>
            <choose>
                <when test="days != null and days > 0">
                    AND createdTime >= CURDATE() - INTERVAL #{days} DAY
                </when>
                <when test="startTime != null and endTime != null">
                    AND createdTime BETWEEN #{startTime} AND #{endTime}
                </when>
               <otherwise>
                   AND DATE(createTime) = CURDATE()
               </otherwise>
            </choose>
            <if test="id != null" >
                AND id = #{id}
            </if>
            <if test="zbName != null and zbName != ''">
                AND zbName = #{zbName}
            </if>
        </where>
    </select>

    <!-- 更新数据（不修改创建时间） -->
    <update id="update" parameterType="LxwzjctsZbHistory">
        UPDATE lxwzjcts_zb_history
        SET
            result = #{result},
            jsonResult = #{jsonResult}
        WHERE id = #{id}
    </update>

    <!-- 根据 ID 删除 -->
    <delete id="deleteById" parameterType="Long">
        DELETE FROM lxwzjcts_zb_history WHERE id = #{id}
    </delete>

    <!-- 查询所有（示例） -->
    <select id="selectAll" resultType="LxwzjctsZbHistory">
        SELECT
            id,
            result,
            jsonResult AS jsonResult,
            createdTime AS createdTime
        FROM lxwzjcts_zb_history
        ORDER BY createdTime DESC
    </select>
</mapper>