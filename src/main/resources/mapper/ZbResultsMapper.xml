<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.lxwz.lxwzdata.Mapper.ZbResultsMapper">
    <!-- 插入数据（自动填充 created_time） -->
    <insert id="insert" parameterType="ZbResults" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO zb_results (result, jsonResult, createdTime,type, name, nickName,wldlName)
        VALUES (#{result}, #{jsonResult}, NOW(),#{type} ,#{name},#{nickName},#{wldlName})
    </insert>

    <!-- 根据 ID 查询 -->
    <select id="selectById" parameterType="Long" resultType="ZbResults">
        SELECT
            id,
            result,
            jsonResult AS jsonResult,
            createdTime AS createdTime
        FROM zb_results
        WHERE id = #{id}
    </select>

    <!-- 更新数据（不修改创建时间） -->
    <update id="update" parameterType="ZbResults">
        UPDATE zb_results
        SET
            result = #{result},
            jsonResult = #{jsonResult}
        WHERE id = #{id}
    </update>

    <!-- 根据 ID 删除 -->
    <delete id="deleteById" parameterType="Long">
        DELETE FROM zb_results WHERE id = #{id}
    </delete>

    <!-- 查询所有（示例） -->
    <select id="selectAll" resultType="ZbResults">
        SELECT
            id,
            result,
            jsonResult AS jsonResult,
            createdTime AS createdTime
        FROM zb_results
        ORDER BY createdTime DESC
    </select>
</mapper>