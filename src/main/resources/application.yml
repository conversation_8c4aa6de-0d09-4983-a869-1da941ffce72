spring:
  datasource:
    url: ********************************************************************
    username: root
    password: 123456
    driver-class-name: com.mysql.cj.jdbc.Driver

mybatis:
  mapper-locations: classpath:mapper/*.xml  # XML??????
  type-aliases-package: com.lxwz.lxwzdata.Entity  # ??????
  configuration:
    map-underscore-to-camel-case: true  # ????????
    default-executor-type: BATCH  # 全局启用批处理模式
    jdbc-type-for-null: NULL    # 处理null值
    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl
  logging:
    level:
      com.lxwz.lxwzdata.Mapper: DEBUG  # 查看完整 SQL

