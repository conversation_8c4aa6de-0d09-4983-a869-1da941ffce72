package com.lxwz.lxwzdata.Config;

import lombok.Data;

// 通用响应包装类
@Data
public class ApiResponse<T> {
    private Integer code;  // 状态码
    private String message; // 消息
    private T data;         // 数据体

    // 构造方法
    public ApiResponse(Integer code, String message, T data) {
        this.code = code;
        this.message = message;
        this.data = data;
    }

    // 静态工厂方法（快速创建成功/失败响应）
    public static <T> ApiResponse<T> success(T data) {
        return new ApiResponse<>(200, "请求成功", data);
    }

    public static <T> ApiResponse<T> error(Integer code, String message) {
        return new ApiResponse<>(code, message, null);
    }

}