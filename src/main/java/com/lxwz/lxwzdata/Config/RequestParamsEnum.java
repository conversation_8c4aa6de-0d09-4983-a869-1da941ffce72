package com.lxwz.lxwzdata.Config;

import lombok.Getter;

@Getter
public enum RequestParamsEnum {
    // 入库率-分子-项目单位
    INBOUND_RATE_XMDW_FZ("111aaaaaaa", "2", "FIRM_NAME", null),
    // 入库率-分母
    INBOUND_RATE_XMDW_FM("111aaaaaaa", "2", "FIRM_NAME",null),

    // 入库率-商品维度-分子
    INBOUND_RATE_SPWD_FZ("111aaaaaaa", "2", null, new String[] {"ZYZD4","PRODUCTNAME"}),
    // 入库率-商品维度-分母
    INBOUND_RATE_SPWD_FM("111aaaaaaa", "2", null, new String[] {"ZYZD4","PRODUCTNAME"});

    // 扫码入库率-分子
//    SCAN_RATE("扫码入库率", "扫码入库率", 33333L),
//    // 扫码入库率-分母
//    SCAN_RATE("扫码入库率", "扫码入库率", 33333L),


    private final String zbId;
    private final String dataType;
    private final String groupName;
    private final String[] groupColumnList;


    RequestParamsEnum(String zbId, String dataType, String groupName, String[] groupColumnList) {
        this.zbId = zbId;
        this.dataType = dataType;
        this.groupName = groupName;
        this.groupColumnList = groupColumnList;
    }
}
