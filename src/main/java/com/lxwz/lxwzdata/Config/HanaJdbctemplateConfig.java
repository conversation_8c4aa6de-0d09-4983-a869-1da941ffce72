package com.lxwz.lxwzdata.Config;

import com.zaxxer.hikari.HikariConfig;
import com.zaxxer.hikari.HikariDataSource;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.jdbc.core.JdbcTemplate;

@Configuration
public class HanaJdbctemplateConfig {

    @Bean("hanaJdbcTemplate")
    public JdbcTemplate hanaJdbcTemplate() {
        HikariConfig config = new HikariConfig();
        config.setJdbcUrl("************************************************");
        config.setUsername("GYL_ZHC");
        config.setPassword("Hana_123456!");
        config.setDriverClassName("com.sap.db.jdbc.Driver");
        return new JdbcTemplate(new HikariDataSource(config));
    }
}
