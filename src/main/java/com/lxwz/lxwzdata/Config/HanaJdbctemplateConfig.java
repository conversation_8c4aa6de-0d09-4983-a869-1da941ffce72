package com.lxwz.lxwzdata.Config;

import com.zaxxer.hikari.HikariConfig;
import com.zaxxer.hikari.HikariDataSource;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.jdbc.core.JdbcTemplate;

@Configuration
public class HanaJdbctemplateConfig {

    @Bean("hanaJdbcTemplate")
    public JdbcTemplate hanaJdbcTemplate() {
        HikariConfig config = new HikariConfig();
        config.setJdbcUrl("************************************************");
        config.setUsername("your-username");
        config.setPassword("your-password");
        config.setDriverClassName("com.sap.db.jdbc.Driver");
        return new JdbcTemplate(new HikariDataSource(config));
    }
}
