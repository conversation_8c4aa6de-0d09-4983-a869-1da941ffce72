package com.lxwz.lxwzdata.Config;

import org.apache.http.impl.client.HttpClientBuilder;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.http.client.HttpComponentsClientHttpRequestFactory;
import org.springframework.web.client.RestTemplate;

@Configuration
public class RestTemplateConfig {

    @Bean
    public RestTemplate restTemplate() {
        // 配置连接池和超时
        HttpComponentsClientHttpRequestFactory factory =
                new HttpComponentsClientHttpRequestFactory(
                        HttpClientBuilder.create().build()
                );
        factory.setConnectTimeout(5000); // 连接超时（ms）
        factory.setReadTimeout(10000);   // 读取超时（ms）

        RestTemplate restTemplate = new RestTemplate(factory);

        // 添加统一请求头（如认证）
        restTemplate.getInterceptors().add((request, body, execution) -> {
            request.getHeaders().add("Authorization", "Bearer your-token");
            return execution.execute(request, body);
        });

        return restTemplate;
    }
}