package com.lxwz.lxwzdata.task;

import com.lxwz.lxwzdata.Service.LxwzjctsZbHistoryService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

@Component
@Slf4j
@RequiredArgsConstructor
public class ScheduledTask {
    private final LxwzjctsZbHistoryService lxwzjctsZbHistoryService;

//    @Scheduled(fixedRate = 5000)
    @Scheduled(cron = "0 * * * * ?") // 秒 分 时 日 月 周
    public void task() {
        log.info("开始执行请求任务================================");
        log.info("ZbHistoryService 实例：{}", lxwzjctsZbHistoryService);
        try {
//            lxwzjctsZbHistoryService.requestModelApi();
            log.info("定时任务执行成功======================");
        } catch (Exception e){
            log.error("定时任务执行失败=========================",e);
        }
//        System.out.print("定时任务执行--------:"+ new Date());
        // 后续这里要请求接口服务-------
    }

}
