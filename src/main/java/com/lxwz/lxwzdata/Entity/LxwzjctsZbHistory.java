package com.lxwz.lxwzdata.Entity;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.sql.Timestamp;

@Data
public class LxwzjctsZbHistory {
    private Long id; // 建议用 Long 类型
    public String result;
    public String jsonResult;
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm", timezone = "GMT+8")
    private Timestamp createdTime; // 创建时间（自动填充）
    public  String searchtype; // 1-项目单位\r\n2-商品维度\r\n3-供应商维度\r\n4-物料大类\r\n5-物料中类\r\n6-物料小类
    public  String searchVal; //

    public String zbName; // 指标名
    public String zbFz; //   指标分子值

    public String zbFm; // 指标分母值
}