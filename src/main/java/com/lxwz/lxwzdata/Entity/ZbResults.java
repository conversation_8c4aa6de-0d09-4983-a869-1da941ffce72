package com.lxwz.lxwzdata.Entity;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import java.sql.Timestamp;

@Data
public class ZbResults {
    private Long id; // 建议用 Long 类型
    public String result;
    public String jsonResult;
    private Timestamp createdTime; // 创建时间（自动填充）
    public  String type; // 1-项目单位\r\n2-商品维度\r\n3-供应商维度\r\n4-物料大类\r\n5-物料中类\r\n6-物料小类

    public String name; // 项目单位维度的字段
    public String nickName; // 项目单位维度的字段2

    public String wldlName; // 物料大类维度字段
}