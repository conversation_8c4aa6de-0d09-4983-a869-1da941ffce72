package com.lxwz.lxwzdata.Controller;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.lxwz.lxwzdata.Config.ApiResponse;
import com.lxwz.lxwzdata.Dto.LxwzjctsZbHistoryDto;
import com.lxwz.lxwzdata.Entity.LxwzjctsZbHistory;
import com.lxwz.lxwzdata.Entity.ZbResults;
import com.lxwz.lxwzdata.Service.HttpApiService;
import com.lxwz.lxwzdata.Service.LxwzjctsZbHistoryService;
import com.lxwz.lxwzdata.Service.ZbResultService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.BindingResult;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;
import java.util.stream.Collectors;

@RestController
@RequestMapping("/api/results")
public class ZbResultController {
    private final ZbResultService zbResultService;
    private final HttpApiService httpApiService;
    private final LxwzjctsZbHistoryService lxwzjctsZbHistoryService;

    @Autowired
    public ZbResultController(ZbResultService zbResultService, HttpApiService httpApiService, LxwzjctsZbHistoryService lxwzjctsZbHistoryService){

        this.zbResultService  = zbResultService;
        this.httpApiService = httpApiService;
        this.lxwzjctsZbHistoryService = lxwzjctsZbHistoryService;
    }


    @PostMapping
    public ResponseEntity<String> addResult(@RequestBody ZbResults results) throws JsonProcessingException {
        ObjectMapper objectMapper = new ObjectMapper();
        ZbResults tmpResults = new ZbResults();
        tmpResults.result = results.result;
        tmpResults.jsonResult = objectMapper.writeValueAsString(results);
        tmpResults.type = results.type;
        tmpResults.name = results.name;
        tmpResults.nickName = results.nickName;
        int rows = zbResultService.addResult(tmpResults);
        return rows > 0 ? ResponseEntity.ok("添加成功") : ResponseEntity.badRequest().body("添加失败");
    }

    @GetMapping
    public ResponseEntity<List<ZbResults>> getAllResults() {
        return ResponseEntity.ok(zbResultService.getAllResults());
    }

    @GetMapping("/test")
    public  ResponseEntity<String> testFun(){
//        String apiUrl = "http://localhost:8080/api/results";
        String apiUrl = "http://localhost:8080/api/results";
        String response = httpApiService.get(apiUrl, String.class);
        System.out.print("========================"+response);
        return ResponseEntity.ok(response);
    }

    //    requestModelApi
    //    zbHistoryService
    @GetMapping("/testFun2")
    public  ResponseEntity<String> testFun2(){
    //        String apiUrl = "http://localhost:8080/api/results";
        String apiUrl = "http://localhost:8080/api/results";
        String response = httpApiService.get(apiUrl, String.class);
        System.out.print("========================"+response);
        lxwzjctsZbHistoryService.requestModelApi();
        return ResponseEntity.ok(response);
    }

    @GetMapping("/selectByCondition")
    public  ResponseEntity<ApiResponse <List<LxwzjctsZbHistory>>> selectByCondition(
            @Valid LxwzjctsZbHistoryDto dto,
            BindingResult bindingResult
            ){
        // 校验参数
        if(bindingResult.hasErrors()){
            String errorMsg = bindingResult.getFieldErrors().stream()
                    .map(error -> error.getField() + ": " + error.getDefaultMessage())
                    .collect(Collectors.joining("; "));
            return ResponseEntity.badRequest().body(ApiResponse.error(400,errorMsg));
        }
        List<LxwzjctsZbHistory> historyList =  lxwzjctsZbHistoryService.getHistoryByCondition(dto);
        return ResponseEntity.ok(ApiResponse.success(historyList));
    }
}
