package com.lxwz.lxwzdata.Controller;

import com.lxwz.lxwzdata.Config.ApiResponse;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * @ClassName AbnormalDataController * @Description 异常数据接口
 * <AUTHOR>
 * @Date 09:14 2025/9/4
 * @Version 1.0
 **/
@RestController
@RequestMapping("/api/abnormalData")
public class AbnormalDataController {



    @GetMapping("/getAbnormalData")
    public ApiResponse getAbnormalData() {


        return ApiResponse.success("");
    }
}
