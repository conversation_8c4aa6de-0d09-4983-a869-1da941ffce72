package com.lxwz.lxwzdata.Controller;

import com.lxwz.lxwzdata.Config.ApiResponse;
import com.lxwz.lxwzdata.Service.AbnormalService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.web.bind.annotation.*;

import java.util.Date;
import java.util.HashMap;
import java.util.Map;

/**
 * @ClassName AbnormalDataController
 * @Description 异常数据接口
 * <AUTHOR>
 * @Date 09:14 2025/9/4
 * @Version 1.0
 **/
@RestController
@RequestMapping("/api/abnormalData")
public class AbnormalDataController {

    @Autowired
    private AbnormalService abnormalService;

    /**
     * 获取异常数据
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @param zbName 指标名称
     * @param zbCode 指标编码
     * @return 异常数据响应
     */
    @GetMapping("/getAbnormalData")
    public ApiResponse<Map<String, Object>> getAbnormalData(
            @RequestParam @DateTimeFormat(pattern = "yyyy-MM-dd") Date startTime,
            @RequestParam @DateTimeFormat(pattern = "yyyy-MM-dd") Date endTime,
            @RequestParam String zbName,
            @RequestParam String zbCode) {

        try {
            // 获取异常简要数据
            AbnormalService.AbnormalSimplified simplified = abnormalService.getSimplified(startTime, endTime, zbCode);

            // 获取异常详细数据
            AbnormalService.AbnormalDetail detail = abnormalService.getDetail(startTime, endTime, zbCode);

            // 构建响应数据
            Map<String, Object> responseData = new HashMap<>();
            responseData.put("异常简要数据", convertToMap(simplified));
            responseData.put("详细异常明细表", convertToMap(detail));

            return ApiResponse.success(responseData);

        } catch (Exception e) {
            return ApiResponse.error(500, "获取异常数据失败: " + e.getMessage());
        }
    }

    /**
     * 将Abnormal对象转换为Map格式
     */
    private Map<String, Object> convertToMap(AbnormalService.Abnormal abnormal) {
        Map<String, Object> result = new HashMap<>();
        result.put("header", abnormal.header);
        result.put("body", abnormal.body);
        return result;
    }
}
