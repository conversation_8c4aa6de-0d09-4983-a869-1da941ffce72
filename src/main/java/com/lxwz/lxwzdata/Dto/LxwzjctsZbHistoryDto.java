package com.lxwz.lxwzdata.Dto;


import lombok.Data;

import javax.validation.constraints.NotNull;
import java.time.LocalDateTime;

@Data
public class LxwzjctsZbHistoryDto {
//    @NotNull(message = "id 不能为空")
//    @NotNull(message = "指标id不能为空")
    private Long id;
    @NotNull(message = "指标名不能为空")
    private String zbName;
    private Integer days;       // 最近N天
    private LocalDateTime startTime;
    private LocalDateTime endTime;

    // 验证注解
//    @Not (message = "开始时间不能为空")
    public LocalDateTime getStartTime() { return startTime; }
}
