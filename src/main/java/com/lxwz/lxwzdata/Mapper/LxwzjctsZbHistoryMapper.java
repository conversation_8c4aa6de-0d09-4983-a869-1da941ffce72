package com.lxwz.lxwzdata.Mapper;

import com.lxwz.lxwzdata.Entity.LxwzjctsZbHistory;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.time.LocalDateTime;
import java.util.List;

@Mapper
public interface LxwzjctsZbHistoryMapper {
    int insert(LxwzjctsZbHistory lxwzjctsZbHistory);        // 对应 XML 中的 <insert id="insert">
    LxwzjctsZbHistory selectById(Long id);         // 对应 <select id="selectById">

    List<LxwzjctsZbHistory> selectByCondition(
            @Param("id") Long id,
            @Param("zbName") String zbName,
            @Param("days") Integer days,
            @Param("startTime") LocalDateTime startTime,
            @Param("endTime") LocalDateTime endTime
    );
    int update(LxwzjctsZbHistory lxwzjctsZbHistory);        // 对应 <update id="update">
    int deleteById(Long id);               // 对应 <delete id="deleteById">
    List<LxwzjctsZbHistory> selectAll();           // 对应 <select id="selectAll">

    int batchInsert(@Param("list") List<LxwzjctsZbHistory> list);  // 批量新增数据


}