package com.lxwz.lxwzdata.Mapper;

import com.lxwz.lxwzdata.Entity.ZbResults;
import org.apache.ibatis.annotations.Mapper;
import java.util.List;

@Mapper
public interface ZbResultsMapper {
    int insert(ZbResults zbResult);        // 对应 XML 中的 <insert id="insert">
    ZbResults selectById(Long id);         // 对应 <select id="selectById">
    int update(ZbResults zbResult);        // 对应 <update id="update">
    int deleteById(Long id);               // 对应 <delete id="deleteById">
    List<ZbResults> selectAll();           // 对应 <select id="selectAll">
}