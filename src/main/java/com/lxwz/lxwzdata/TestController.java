package com.lxwz.lxwzdata;

import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController // 标识为REST控制器，直接返回数据而非视图
@RequestMapping("/api") // 类级别路由前缀（可选）
public class TestController {

    // 定义测试方法及路由
    @RequestMapping("/testgxx") // 映射URL: /api/testgxx
    public String testgxx() {
        return "TestGXX Success!23335555";
    }
}
