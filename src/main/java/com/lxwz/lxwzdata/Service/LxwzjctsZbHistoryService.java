package com.lxwz.lxwzdata.Service;

import com.lxwz.lxwzdata.Config.RequestParamsEnum;
import com.lxwz.lxwzdata.Dto.LxwzjctsZbHistoryDto;
import com.lxwz.lxwzdata.Entity.LxwzjctsZbHistory;
import com.lxwz.lxwzdata.Mapper.LxwzjctsZbHistoryMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;

@Service
public class LxwzjctsZbHistoryService {
    private final LxwzjctsZbHistoryMapper lxwzjctsZbHistoryMapper;
    private final HttpApiService httpApiService;


    @Autowired
    public LxwzjctsZbHistoryService(LxwzjctsZbHistoryMapper lxwzjctsZbHistoryMapper, HttpApiService httpApiService){

        this.lxwzjctsZbHistoryMapper = lxwzjctsZbHistoryMapper;
        this.httpApiService = httpApiService;

    }
    @Transactional
    public int addResult(com.lxwz.lxwzdata.Entity.LxwzjctsZbHistory results){
        return this.lxwzjctsZbHistoryMapper.insert(results);
    }
    @Transactional
    public int updateResult(com.lxwz.lxwzdata.Entity.LxwzjctsZbHistory results){
        return lxwzjctsZbHistoryMapper.update(results);
    }
    public LxwzjctsZbHistory getResultById(Long id){
        return lxwzjctsZbHistoryMapper.selectById(id);
    }

    ;
    public List<LxwzjctsZbHistory> getHistoryByCondition(LxwzjctsZbHistoryDto dto){
        System.out.print("'==================================service============================='");
        System.out.print(dto.getDays());
        System.out.print(dto.getZbName());
//        return lxwzjctsZbHistoryMapper.selectByCondition(dto.getId(),dto.getZbName(),dto.getDays(),null,null);
        return lxwzjctsZbHistoryMapper.selectByCondition(dto.getId(),dto.getZbName(), dto.getDays(),null,null);
    }
    public List<com.lxwz.lxwzdata.Entity.LxwzjctsZbHistory> getAllResults(){
        return this.lxwzjctsZbHistoryMapper.selectAll();
    }


    // 发起请求-入库率数据
    public int requestModelApi(){
        String requestUrl = "https://www.baidu.com";
        RequestParamsEnum.INBOUND_RATE_SPWD_FZ.getZbId();
//        for (RequestParamsEnum param:RequestParamsEnum.values()){
//            Map<String, Object> body = new HashMap<>();
//            body.put("a", param.getZbId());
//            body.put("bbb", param.getDataType());
//            // 发送请求
//            String response = httpApiService.postJson(requestUrl, body, String.class);
//            System.out.printf("请求成功，响应:", response);
//        }
        batchInsert();
//        RequestParamsEnum.INBOUND_RATE.getZbId();
//        httpApiService.postJson()
        return 1;
    }


//    执行批量插入数据
//public int batchInsert(List<ZbHistory> zbHistories){
    public int batchInsert(){
        System.out.print("执行批量插入---------------------");
        List<LxwzjctsZbHistory> List = Arrays.asList(new LxwzjctsZbHistory[]{
                createRandomRecord(),
                createRandomRecord(),
                createRandomRecord()
        });
        System.out.print("生成批量数据成功---------------------"+List);
        lxwzjctsZbHistoryMapper.batchInsert(List);
        return 2;
    }

    private LxwzjctsZbHistory createRandomRecord() {
         Random random = new Random();
        LxwzjctsZbHistory record = new LxwzjctsZbHistory();

        record.setResult("123");
        record.setJsonResult("{\"code\":" + random.nextInt(100) + ",\"msg\":\"" + "成功" + "\"}");
        record.setSearchtype(String.valueOf(1 + random.nextInt(6))); // 1-6随机
        record.setZbName("xxxxx指标"); // 随机指标名
        record.setZbFz(generateRandomDouble(100, 10000));
        record.setZbFm(generateRandomDouble(1, 9999));
        return record;
    }
    private String generateRandomDouble(double min, double max) {
        Random random = new Random();
        return String.format("%.2f", min + (max - min) * random.nextDouble());
    }
}
