package com.lxwz.lxwzdata.Service;

import com.lxwz.lxwzdata.Mapper.ZbResultsMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

@Service
public class ZbResultService {
    private final ZbResultsMapper zbResults;

    @Autowired
    public ZbResultService(ZbResultsMapper zbResults){
        this.zbResults = zbResults;
    }
    @Transactional
    public int addResult(com.lxwz.lxwzdata.Entity.ZbResults results){
        return this.zbResults.insert(results);
    }
    @Transactional
    public int updateResult(com.lxwz.lxwzdata.Entity.ZbResults results){
        return zbResults.update(results);
    }
    public com.lxwz.lxwzdata.Entity.ZbResults getResultById(Long id){
        return zbResults.selectById(id);
    }
    public List<com.lxwz.lxwzdata.Entity.ZbResults> getAllResults(){
        System.out.print("344444444444444444444444");
        System.out.print("==========================================");
        return zbResults.selectAll();
    }
}
