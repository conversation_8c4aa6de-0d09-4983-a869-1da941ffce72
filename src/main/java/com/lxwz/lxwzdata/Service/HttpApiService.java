package com.lxwz.lxwzdata.Service;

import org.springframework.http.*;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

import java.util.Map;

@Service
public class HttpApiService {
    private final RestTemplate restTemplate;

    public HttpApiService(RestTemplate restTemplate) {
        this.restTemplate = restTemplate;
    }

    // GET 请求（带路径参数）
    public <T> T get(String url, Class<T> responseType, Object... uriVariables) {
        return restTemplate.getForObject(url, responseType, uriVariables);
    }

    // GET 请求（带查询参数）
    public <T> T getWithParams(String url, Class<T> responseType, Map<String, ?> params) {
        return restTemplate.getForObject(url, responseType, params);
    }

    // POST 请求（JSON 格式）
    public <T> T postJson(String url, Object requestBody, Class<T> responseType) {
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);
        HttpEntity<Object> entity = new HttpEntity<>(requestBody, headers);
        return restTemplate.postForObject(url, entity, responseType);
    }

    // 统一异常处理
    public <T> ResponseEntity<T> exchange(String url, HttpMethod method, Object body, Class<T> responseType) {
        try {
            HttpEntity<?> entity = new HttpEntity<>(body);
            return restTemplate.exchange(url, method, entity, responseType);
        } catch (Exception e) {
            throw new RuntimeException("HTTP请求失败: " + e.getMessage());
        }
    }
}