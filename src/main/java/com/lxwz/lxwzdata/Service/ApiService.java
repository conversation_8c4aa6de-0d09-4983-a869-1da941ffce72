package com.lxwz.lxwzdata.Service;


import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.web.client.ResourceAccessException;
import org.springframework.web.client.RestTemplate;

@Service
public class ApiService {
    @Autowired
    @Qualifier("restTemplateForGet")
    private static RestTemplate restTemplateForGet;

    public static String safeRequest(String url, Object body){
        try {
            System.err.println("safeRequest--------------------------------------------------");
            HttpHeaders headers = new HttpHeaders();
            HttpEntity<Object> request = new HttpEntity<>(body, headers);
            ResponseEntity<String> response = restTemplateForGet.exchange(url, HttpMethod.GET, request,String.class);
            return response.getBody();
        } catch (ResourceAccessException e){
                // 捕获异常并解析错误信息
//            System.err.println("状态码: " + e.getStatusCode());
//            System.err.println("响应体: " + e.getResponseBodyAsString()); // 获取错误详情
            System.err.println("报错了--------------------------------------------------");
            return null;
        }
    }


}
