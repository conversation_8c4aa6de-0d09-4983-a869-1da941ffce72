package com.lxwz.lxwzdata.Service;

import java.util.Date;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Service;

/**
 * 异常数据接入服务
 */
@Service
public class AbnormalService {
    @Qualifier("hanaJdbcTemplate")
    private JdbcTemplate hanaJdbcTemplate;

    // 异常基础表
    public abstract class Abnormal {
        // 表头
        public String[] header;
        // 数据
        public String[][] body;
    }
    // 异常详情
    public class AbnormalDetail extends Abnormal {}
    // 精简版异常
    public class AbnormalSimplified extends Abnormal{}

    /**
     * 获取异常详情
     * @param start 开始时间
     * @param end 结束时间
     * @param zbID 指标 ID
     * @return 异常详情
     */
    public AbnormalDetail getDetail(Date start, Date end, String zbID){

        AbnormalDetail  a = new AbnormalDetail();
        return new AbnormalDetail();
    }

    /**
     * 获取异常详情
     * @param start 开始时间
     * @param end 结束时间
     * @param zbID 指标 ID
     * @return 异常详情
     */
    public AbnormalSimplified getSimplified(Date start, Date end, String zbID){
        AbnormalSimplified  a = new AbnormalSimplified();
        return new AbnormalSimplified();
    }
}
