package com.lxwz.lxwzdata.Service;

import java.text.SimpleDateFormat;
import java.util.*;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Service;

/**
 * 异常数据接入服务
 */
@Service
public class AbnormalService {

    @Autowired
    @Qualifier("hanaJdbcTemplate")
    private JdbcTemplate hanaJdbcTemplate;

    private final SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd");

    // 异常基础表
    public static class Abnormal {
        // 表头
        public String[] header;
        // 数据
        public String[][] body;

        public Abnormal() {
        }

        public Abnormal(String[] header, String[][] body) {
            this.header = header;
            this.body = body;
        }
    }

    // 异常详情
    public static class AbnormalDetail extends Abnormal {
        public AbnormalDetail() {
        }

        public AbnormalDetail(String[] header, String[][] body) {
            super(header, body);
        }
    }

    // 精简版异常
    public static class AbnormalSimplified extends Abnormal {
        public AbnormalSimplified() {
        }

        public AbnormalSimplified(String[] header, String[][] body) {
            super(header, body);
        }
    }

    /**
     * 获取异常详情
     *
     * @param start 开始时间
     * @param end   结束时间
     * @param zbID  指标 ID
     * @return 异常详情
     */
    public AbnormalDetail getDetail(Date start, Date end, String zbID) {
        switch (zbID) {
            case "BQBD":
                return getBQBDDetail(start, end);
            case "WZRKL":
                return getWZRKLDetail(start, end);
            case "SMRK":
                return getSMRKDetail(start, end);
            case "JSJSX":
                return getJSJSXDetail(start, end);
            case "XGSP":
                return getXGSPDetail(start, end);
            case "WZJY":
                return getWZJYDetail(start, end);
            case "ZHQXFL":
                return getZHQXFLDetail(start, end);
            case "DTLY":
                return getDTLYDetail(start, end);
            default:
                return new AbnormalDetail();
        }
    }

    /**
     * 获取异常简要数据
     *
     * @param start 开始时间
     * @param end   结束时间
     * @param zbID  指标 ID
     * @return 异常简要数据
     */
    public AbnormalSimplified getSimplified(Date start, Date end, String zbID) {
        switch (zbID) {
            case "BQBD":
                return getBQBDSimplified(start, end);
            case "WZRKL":
                return getWZRKLSimplified(start, end);
            case "SMRK":
                return getSMRKSimplified(start, end);
            case "JSJSX":
                return getJSJSXSimplified(start, end);
            case "XGSP":
                return getXGSPSimplified(start, end);
            case "WZJY":
                return getWZJYSimplified(start, end);
            case "ZHQXFL":
                return getZHQXFLSimplified(start, end);
            case "DTLY":
                return getDTLYSimplified(start, end);
            default:
                return new AbnormalSimplified();
        }
    }

    // ==================== 电商非电网物资－物资标签绑定率 (BQBD) ====================

    /**
     * 获取物资标签绑定率详细异常数据
     */
    private AbnormalDetail getBQBDDetail(Date start, Date end) {
        String sql = "SELECT EBELN, EBELP, WERKS, BWART, ELIKZ, MATNR, ZGZSJ, WQSHSJ, " +
                "ZYLZD4, PRODUCTNAME, SFRK, SFTB, WH_NO, WH_NAME, FIRM_NAME, SIGN_NAME, " +
                "SIGN_TIME, VENDOR, MATERIAL_CATEGORY_NAME, MATERIAL_MIDDLE_NAME, " +
                "MATERIAL_SMALL_NAME, OVER_NUM, SCAN_MATERIAL_NUM, CREATE_TIME, LABEL_CODE, " +
                "CONNECT_NAME, CONNECT_TIME, OVER_TIME, BQSBSL " +
                "FROM SCM_LJMX_DS_BQBD " +
                "WHERE FIRM_NAME IS NOT NULL AND MATERIAL_CATEGORY_NAME IS NOT NULL " +
                "AND MATERIAL_MIDDLE_NAME IS NOT NULL AND MATERIAL_SMALL_NAME IS NOT NULL " +
                "AND PRODUCTNAME IS NOT NULL " +
                "AND OVER_NUM != SCAN_MATERIAL_NUM " +
                "AND CREATE_TIME >= ? AND CREATE_TIME <= ? " +
                "ORDER BY CREATE_TIME DESC";

        List<Map<String, Object>> results = hanaJdbcTemplate.queryForList(sql,
                dateFormat.format(start), dateFormat.format(end));

        String[] header = {
                "采购凭证号", "采购凭证行号", "工厂编码", "移动类型",
                "交货已完成", "物料", "过账时间", "完全收货时间",
                "商品ID", "商品名称", "是否入库", "是否贴标",
                "仓库编码", "仓库名称", "项目单位名称", "仓库签收人",
                "签收日期", "供应商", "物料大类名称", "物料中类名称",
                "物料小类名称", "已入库数量", "已扫描数量", "入库操作时间",
                "是否扫码入库", "领料人", "领料日期", "出库操作时间",
                "标签识别数量"
        };

        String[][] body = results.stream()
                .map(row -> new String[]{
                        String.valueOf(row.get("EBELN")),
                        String.valueOf(row.get("EBELP")),
                        String.valueOf(row.get("WERKS")),
                        String.valueOf(row.get("BWART")),
                        String.valueOf(row.get("ELIKZ")),
                        String.valueOf(row.get("MATNR")),
                        String.valueOf(row.get("ZGZSJ")),
                        String.valueOf(row.get("WQSHSJ")),
                        String.valueOf(row.get("ZYLZD4")),
                        String.valueOf(row.get("PRODUCTNAME")),
                        String.valueOf(row.get("SFRK")),
                        String.valueOf(row.get("SFTB")),
                        String.valueOf(row.get("WH_NO")),
                        String.valueOf(row.get("WH_NAME")),
                        String.valueOf(row.get("FIRM_NAME")),
                        String.valueOf(row.get("SIGN_NAME")),
                        String.valueOf(row.get("SIGN_TIME")),
                        String.valueOf(row.get("VENDOR")),
                        String.valueOf(row.get("MATERIAL_CATEGORY_NAME")),
                        String.valueOf(row.get("MATERIAL_MIDDLE_NAME")),
                        String.valueOf(row.get("MATERIAL_SMALL_NAME")),
                        String.valueOf(row.get("OVER_NUM")),
                        String.valueOf(row.get("SCAN_MATERIAL_NUM")),
                        String.valueOf(row.get("CREATE_TIME")),
                        String.valueOf(row.get("LABEL_CODE")),
                        String.valueOf(row.get("CONNECT_NAME")),
                        String.valueOf(row.get("CONNECT_TIME")),
                        String.valueOf(row.get("OVER_TIME")),
                        String.valueOf(row.get("BQSBSL"))
                })
                .toArray(String[][]::new);

        return new AbnormalDetail(header, body);
    }

    /**
     * 获取物资标签绑定率简要数据
     */
    private AbnormalSimplified getBQBDSimplified(Date start, Date end) {
        String sql = "SELECT EBELN, EBELP, VENDOR, MATERIAL_CATEGORY_NAME, " +
                "MATERIAL_MIDDLE_NAME, MATERIAL_SMALL_NAME, OVER_NUM, SCAN_MATERIAL_NUM " +
                "FROM SCM_LJMX_DS_BQBD " +
                "WHERE FIRM_NAME IS NOT NULL AND MATERIAL_CATEGORY_NAME IS NOT NULL " +
                "AND MATERIAL_MIDDLE_NAME IS NOT NULL AND MATERIAL_SMALL_NAME IS NOT NULL " +
                "AND PRODUCTNAME IS NOT NULL " +
                "AND OVER_NUM != SCAN_MATERIAL_NUM " +
                "AND CREATE_TIME >= ? AND CREATE_TIME <= ? " +
                "ORDER BY CREATE_TIME DESC";

        List<Map<String, Object>> results = hanaJdbcTemplate.queryForList(sql,
                dateFormat.format(start), dateFormat.format(end));

        String[] header = {"采购凭证号", "采购凭证行号", "供应商名称", "物料大类",
                "物料中类", "物料小类", "已入库数量", "已扫描数量"};

        String[][] body = results.stream()
                .map(row -> new String[]{
                        String.valueOf(row.get("EBELN")),
                        String.valueOf(row.get("EBELP")),
                        String.valueOf(row.get("VENDOR")),
                        String.valueOf(row.get("MATERIAL_CATEGORY_NAME")),
                        String.valueOf(row.get("MATERIAL_MIDDLE_NAME")),
                        String.valueOf(row.get("MATERIAL_SMALL_NAME")),
                        String.valueOf(row.get("OVER_NUM")),
                        String.valueOf(row.get("SCAN_MATERIAL_NUM"))
                })
                .toArray(String[][]::new);

        return new AbnormalSimplified(header, body);
    }

    // ==================== 电商非电网物资－物资入库率 (WZRKL) ====================

    /**
     * 获取物资入库率详细异常数据
     */
    private AbnormalDetail getWZRKLDetail(Date start, Date end) {
        String sql = "SELECT ORDER_NUMBER, ORDER_NUMBER_LINE, WERKS, BWART, ELIKZ, MATNR, ZGZSJ, ERP_RECEIVER, " +
                "WQSHSJ, ZYLZD4, PRODUCTNAME, SFRK, SFTB, WH_NO, WH_NAME, FIRM_NAME, SIGN_NAME_RUKU, " +
                "SIGN_TIME, VENDOR, MATERIAL_CATEGORY_NAME, MATERIAL_MIDDLE_NAME, MATERIAL_SMALL_NAME, " +
                "OVER_NUM_RUKU, SCAN_MATERIAL_NUM, CREATE_TIME, LABEL_CODE, SIGN_NAME_CHUKU, CONNECT_NAME, " +
                "CONNECT_TIME, OVER_NUM_CHUKU, OVER_TIME, XDSHDZ, SMSHNAME, SMSHTIME, SMSHDZ, " +
                "YCQSRZH, YCQSRNAME, YCQSTIME " +
                "FROM SCM_LJMX_DS_CRK " +
                "WHERE ELIKZ = 'X' " +
                "AND FIRM_NAME IS NOT NULL AND MATERIAL_CATEGORY_NAME IS NOT NULL " +
                "AND MATERIAL_MIDDLE_NAME IS NOT NULL AND MATERIAL_SMALL_NAME IS NOT NULL " +
                "AND PRODUCTNAME IS NOT NULL " +
                "AND SFRK ='否' " +
                "AND CREATE_TIME >= ? AND CREATE_TIME <= ? " +
                "ORDER BY CREATE_TIME DESC";

        List<Map<String, Object>> results = hanaJdbcTemplate.queryForList(sql,
                dateFormat.format(start), dateFormat.format(end));

        String[] header = {
                "采购凭证号（订单号）", "采购凭证行号（订单行号）", "工厂编码", "移动类型",
                "交货已完成", "物料", "过账时间", "ERP收货人账号",
                "完全收货时间", "商品ID", "商品名称", "是否入库",
                "是否贴标", "仓库编码", "仓库名称", "项目单位名称",
                "仓库签收人", "签收日期", "供应商", "物料大类名称",
                "物料中类名称", "物料小类名称", "已入库数量", "已扫描数量",
                "入库操作时间", "是否扫码入库", "出库操作人", "领料人",
                "领料日期", "已出库数量", "出库操作时间", "下单收货地址",
                "扫码收货姓名", "扫码收货时间", "扫码收货地址", "远程签收人账号",
                "远程签收人姓名", "远程签收时间"
        };

        String[][] body = results.stream()
                .map(row -> new String[]{
                        String.valueOf(row.get("ORDER_NUMBER")),
                        String.valueOf(row.get("ORDER_NUMBER_LINE")),
                        String.valueOf(row.get("WERKS")),
                        String.valueOf(row.get("BWART")),
                        String.valueOf(row.get("ELIKZ")),
                        String.valueOf(row.get("MATNR")),
                        String.valueOf(row.get("ZGZSJ")),
                        String.valueOf(row.get("ERP_RECEIVER")),
                        String.valueOf(row.get("WQSHSJ")),
                        String.valueOf(row.get("ZYLZD4")),
                        String.valueOf(row.get("PRODUCTNAME")),
                        String.valueOf(row.get("SFRK")),
                        String.valueOf(row.get("SFTB")),
                        String.valueOf(row.get("WH_NO")),
                        String.valueOf(row.get("WH_NAME")),
                        String.valueOf(row.get("FIRM_NAME")),
                        String.valueOf(row.get("SIGN_NAME_RUKU")),
                        String.valueOf(row.get("SIGN_TIME")),
                        String.valueOf(row.get("VENDOR")),
                        String.valueOf(row.get("MATERIAL_CATEGORY_NAME")),
                        String.valueOf(row.get("MATERIAL_MIDDLE_NAME")),
                        String.valueOf(row.get("MATERIAL_SMALL_NAME")),
                        String.valueOf(row.get("OVER_NUM_RUKU")),
                        String.valueOf(row.get("SCAN_MATERIAL_NUM")),
                        String.valueOf(row.get("CREATE_TIME")),
                        String.valueOf(row.get("LABEL_CODE")),
                        String.valueOf(row.get("SIGN_NAME_CHUKU")),
                        String.valueOf(row.get("CONNECT_NAME")),
                        String.valueOf(row.get("CONNECT_TIME")),
                        String.valueOf(row.get("OVER_NUM_CHUKU")),
                        String.valueOf(row.get("OVER_TIME")),
                        String.valueOf(row.get("XDSHDZ")),
                        String.valueOf(row.get("SMSHNAME")),
                        String.valueOf(row.get("SMSHTIME")),
                        String.valueOf(row.get("SMSHDZ")),
                        String.valueOf(row.get("YCQSRZH")),
                        String.valueOf(row.get("YCQSRNAME")),
                        String.valueOf(row.get("YCQSTIME"))
                })
                .toArray(String[][]::new);

        return new AbnormalDetail(header, body);
    }

    /**
     * 获取物资入库率简要数据
     */
    private AbnormalSimplified getWZRKLSimplified(Date start, Date end) {
        String sql = "SELECT EBELN, EBELP, FIRM_NAME, VENDOR, MATERIAL_CATEGORY_NAME, " +
                "MATERIAL_MIDDLE_NAME, MATERIAL_SMALL_NAME " +
                "FROM SCM_LJMX_DS_CRK " +
                "WHERE ELIKZ = 'X' " +
                "AND FIRM_NAME IS NOT NULL AND MATERIAL_CATEGORY_NAME IS NOT NULL " +
                "AND MATERIAL_MIDDLE_NAME IS NOT NULL AND MATERIAL_SMALL_NAME IS NOT NULL " +
                "AND PRODUCTNAME IS NOT NULL " +
                "AND SFRK ='否' " +
                "AND CREATE_TIME >= ? AND CREATE_TIME <= ? " +
                "ORDER BY CREATE_TIME DESC";

        List<Map<String, Object>> results = hanaJdbcTemplate.queryForList(sql,
                dateFormat.format(start), dateFormat.format(end));

        String[] header = {"采购订单号", "采购订单行号", "项目单位名称", "供应商名称",
                "物料大类", "物料中类", "物料小类"};

        String[][] body = results.stream()
                .map(row -> new String[]{
                        String.valueOf(row.get("EBELN")),
                        String.valueOf(row.get("EBELP")),
                        String.valueOf(row.get("FIRM_NAME")),
                        String.valueOf(row.get("VENDOR")),
                        String.valueOf(row.get("MATERIAL_CATEGORY_NAME")),
                        String.valueOf(row.get("MATERIAL_MIDDLE_NAME")),
                        String.valueOf(row.get("MATERIAL_SMALL_NAME"))
                })
                .toArray(String[][]::new);

        return new AbnormalSimplified(header, body);
    }

    // ==================== 电商非电网物资－物资扫码入库率 (SMRK) ====================

    /**
     * 获取物资扫码入库率详细异常数据
     */
    private AbnormalDetail getSMRKDetail(Date start, Date end) {
        String sql = "SELECT ORDER_NUMBER, ORDER_NUMBER_LINE, VENDOR, MATERIAL_CATEGORY_NAME, " +
                "MATERIAL_MIDDLE_NAME, MATERIAL_SMALL_NAME, OVER_NUM_RUKU, SCAN_MATERIAL_NUM, " +
                "CREATE_TIME, LABEL_CODE, WH_NO, WH_NAME, SIGN_NAME_RUKU, FIRM_NAME, SIGN_TIME, " +
                "OVER_NUM_CHUKU, OVER_TIME, SIGN_NAME_CHUKU, CONNECT_NAME, CONNECT_TIME, " +
                "ZYLZD4, PRODUCTNAME, SFRK, SFTB " +
                "FROM SCM_LJMX_DS_CRKSM " +
                "WHERE VENDOR IS NOT NULL AND MATERIAL_CATEGORY_NAME IS NOT NULL " +
                "AND MATERIAL_MIDDLE_NAME IS NOT NULL AND MATERIAL_SMALL_NAME IS NOT NULL " +
                "AND (IS_SCAN_STORED != IS_STORED) " +
                "AND CREATE_TIME >= ? AND CREATE_TIME <= ? " +
                "ORDER BY CREATE_TIME DESC";

        List<Map<String, Object>> results = hanaJdbcTemplate.queryForList(sql,
                dateFormat.format(start), dateFormat.format(end));

        String[] header = {
                "采购订单号", "采购订单行号", "供应商", "物料大类名称",
                "物料中类名称", "物料小类名称", "已入库数量", "已扫描数量",
                "入库操作时间", "是否扫码入库", "仓库编码", "仓库名称",
                "仓库签收人", "项目单位名称", "签收日期", "已出库数量",
                "出库操作时间", "出库操作人", "领料人", "领料日期",
                "商品ID", "商品名称", "是否入库", "是否贴标"
        };

        String[][] body = results.stream()
                .map(row -> new String[]{
                        String.valueOf(row.get("ORDER_NUMBER")),
                        String.valueOf(row.get("ORDER_NUMBER_LINE")),
                        String.valueOf(row.get("VENDOR")),
                        String.valueOf(row.get("MATERIAL_CATEGORY_NAME")),
                        String.valueOf(row.get("MATERIAL_MIDDLE_NAME")),
                        String.valueOf(row.get("MATERIAL_SMALL_NAME")),
                        String.valueOf(row.get("OVER_NUM_RUKU")),
                        String.valueOf(row.get("SCAN_MATERIAL_NUM")),
                        String.valueOf(row.get("CREATE_TIME")),
                        String.valueOf(row.get("LABEL_CODE")),
                        String.valueOf(row.get("WH_NO")),
                        String.valueOf(row.get("WH_NAME")),
                        String.valueOf(row.get("SIGN_NAME_RUKU")),
                        String.valueOf(row.get("FIRM_NAME")),
                        String.valueOf(row.get("SIGN_TIME")),
                        String.valueOf(row.get("OVER_NUM_CHUKU")),
                        String.valueOf(row.get("OVER_TIME")),
                        String.valueOf(row.get("SIGN_NAME_CHUKU")),
                        String.valueOf(row.get("CONNECT_NAME")),
                        String.valueOf(row.get("CONNECT_TIME")),
                        String.valueOf(row.get("ZYLZD4")),
                        String.valueOf(row.get("PRODUCTNAME")),
                        String.valueOf(row.get("SFRK")),
                        String.valueOf(row.get("SFTB"))
                })
                .toArray(String[][]::new);

        return new AbnormalDetail(header, body);
    }

    /**
     * 获取物资扫码入库率简要数据
     */
    private AbnormalSimplified getSMRKSimplified(Date start, Date end) {
        String sql = "SELECT ORDER_NUMBER, ORDER_NUMBER_LINE, VENDOR, MATERIAL_CATEGORY_NAME, " +
                "MATERIAL_MIDDLE_NAME, MATERIAL_SMALL_NAME, OVER_NUM_RUKU, SCAN_MATERIAL_NUM " +
                "FROM SCM_LJMX_DS_CRKSM " +
                "WHERE VENDOR IS NOT NULL AND MATERIAL_CATEGORY_NAME IS NOT NULL " +
                "AND MATERIAL_MIDDLE_NAME IS NOT NULL AND MATERIAL_SMALL_NAME IS NOT NULL " +
                "AND (IS_SCAN_STORED != IS_STORED) " +
                "AND CREATE_TIME >= ? AND CREATE_TIME <= ? " +
                "ORDER BY CREATE_TIME DESC";

        List<Map<String, Object>> results = hanaJdbcTemplate.queryForList(sql,
                dateFormat.format(start), dateFormat.format(end));

        String[] header = {"采购凭证号", "采购凭证行号", "供应商名称", "物料大类",
                "物料中类", "物料小类", "已入库数量", "已扫描数量"};

        String[][] body = results.stream()
                .map(row -> new String[]{
                        String.valueOf(row.get("ORDER_NUMBER")),
                        String.valueOf(row.get("ORDER_NUMBER_LINE")),
                        String.valueOf(row.get("VENDOR")),
                        String.valueOf(row.get("MATERIAL_CATEGORY_NAME")),
                        String.valueOf(row.get("MATERIAL_MIDDLE_NAME")),
                        String.valueOf(row.get("MATERIAL_SMALL_NAME")),
                        String.valueOf(row.get("OVER_NUM_RUKU")),
                        String.valueOf(row.get("SCAN_MATERIAL_NUM"))
                })
                .toArray(String[][]::new);

        return new AbnormalSimplified(header, body);
    }

    // ==================== 电商非电网物资－结算及时性分析 (JSJSX) ====================

    /**
     * 获取结算及时性分析详细异常数据
     */
    private AbnormalDetail getJSJSXDetail(Date start, Date end) {
        String sql = "SELECT EBELN, EBELP, ZQGDH, ZGGDHXM, ERNAM, AFNAM, QGSQRPZH, BADAT, " +
                "USERNAME_XQSHR, XQSHRXM, XQSHRPZH, UDATE_XQSHR, UTIME_XQSHR, USERNAME_XQSPR, " +
                "XQSPRXM, XQSPRPZH, UDATE_XQSPR, UTIME_XQSPR, USERNAME_DDSPR, DDSPRXM, DDSPRPZH, " +
                "UDATE_DDSPR, UTIME_DDSPR, USNAM, XTSHRXM, XTSHRPZH, BUDAT, MATNR, ZJSCS, ZKZBM, " +
                "TXT_B, TXT_M, WGBEZ, ZYLZD4, PRODUCTNAME, MENGE, ZDD_HSDJEINDT, ZHSZJ, ZXMBH, " +
                "ZXMMC, WERKS, KNTTP, MCSX, CC_NAME, WH_NAME, ZPTEXT1, ZPTEXT2, ZPTEXT3, WBSYS, " +
                "FBUDAT, NAME_TEXTC, JSZFSC, HTBFQRPZH, XDSHDZ, SMSHRZH, SMSHNAME, SMSHTIME, " +
                "SMSHGCMC, SMSHPZH, SMSHDZ, YCQSRZH, YCQSRNAME, YCQSTIME, YCQSGCMC, YCQSRPZH, " +
                "SHI1, SHI2, SHI3, SHI4, SHI5, SHI6, SHI7, SHI8, SHI9, SHZSC, QGFKSC, ZNF, SFZDWZ " +
                "FROM SCM_LJMX_DS_ZBFX " +
                "WHERE MCSX IS NOT NULL AND CC_NAME IS NOT NULL " +
                "AND TXT_B IS NOT NULL AND TXT_M IS NOT NULL AND WGBEZ IS NOT NULL " +
                "AND JSZFSC > 60 " +
                "AND BUDAT >= ? AND BUDAT <= ? " +
                "ORDER BY BUDAT DESC";

        List<Map<String, Object>> results = hanaJdbcTemplate.queryForList(sql,
                dateFormat.format(start), dateFormat.format(end));

        String[] header = {
                "采购凭证号", "采购凭证行号", "请购单号", "请购单行号",
                "请购人姓名", "请购申请人账号", "请购申请人P账号", "请购时间",
                "需求审核人账号", "需求审核人姓名", "需求审核人P账号", "需求审核日期",
                "需求审核时间", "需求审批人账号", "需求审批人姓名", "需求审批人P账号",
                "需求审批人日期", "需求审批人时间", "订单审批人账号", "订单审批人姓名",
                "订单审批人P账号", "订单审批日期", "订单审批时间", "ERP系统收货人账号",
                "ERP系统收货人姓名", "ERP系统收货人P账号", "ERP系统收货时间", "物料编号",
                "物料扩展描述", "物料扩展编码", "物料大类描述", "物料中类描述",
                "物料小类描述", "商品编码", "商品名称", "购买数量",
                "单价（含税）", "行项目总金额（含税）", "项目编号", "项目名称",
                "工厂编码", "科目分配类别", "工厂名称", "供应商名称",
                "仓库名称", "项目大类", "项目中类", "项目小类",
                "WBS元素", "合同部发起支付时间", "合同部发起申请人", "结算支付时长",
                "合同部发起人P账号", "下单收货地址", "扫码收货人账号", "扫码收货姓名",
                "扫码收货时间", "扫码收货人所在工厂", "扫码收货人P账号", "扫码收货地址",
                "远程签收人账号", "远程签收人姓名", "远程签收时间", "远程签收人所属工厂",
                "远程签收人P账号", "请购申请人-需求审核人", "请购申请人-需求审批人", "请购申请人-订单审批人",
                "请购申请人-扫码收货人", "请购申请人-远程签收人", "请购申请人-系统收货人", "需求审核人-需求审批人",
                "需求审批人-订单审批人", "需求审核人-订单审批人", "收货总时长", "请购付款时长",
                "总年份", "是否重点物资"
        };

        String[][] body = results.stream()
                .map(row -> new String[]{
                        String.valueOf(row.get("EBELN")),
                        String.valueOf(row.get("EBELP")),
                        String.valueOf(row.get("ZQGDH")),
                        String.valueOf(row.get("ZGGDHXM")),
                        String.valueOf(row.get("ERNAM")),
                        String.valueOf(row.get("AFNAM")),
                        String.valueOf(row.get("QGSQRPZH")),
                        String.valueOf(row.get("BADAT")),
                        String.valueOf(row.get("USERNAME_XQSHR")),
                        String.valueOf(row.get("XQSHRXM")),
                        String.valueOf(row.get("XQSHRPZH")),
                        String.valueOf(row.get("UDATE_XQSHR")),
                        String.valueOf(row.get("UTIME_XQSHR")),
                        String.valueOf(row.get("USERNAME_XQSPR")),
                        String.valueOf(row.get("XQSPRXM")),
                        String.valueOf(row.get("XQSPRPZH")),
                        String.valueOf(row.get("UDATE_XQSPR")),
                        String.valueOf(row.get("UTIME_XQSPR")),
                        String.valueOf(row.get("USERNAME_DDSPR")),
                        String.valueOf(row.get("DDSPRXM")),
                        String.valueOf(row.get("DDSPRPZH")),
                        String.valueOf(row.get("UDATE_DDSPR")),
                        String.valueOf(row.get("UTIME_DDSPR")),
                        String.valueOf(row.get("USNAM")),
                        String.valueOf(row.get("XTSHRXM")),
                        String.valueOf(row.get("XTSHRPZH")),
                        String.valueOf(row.get("BUDAT")),
                        String.valueOf(row.get("MATNR")),
                        String.valueOf(row.get("ZJSCS")),
                        String.valueOf(row.get("ZKZBM")),
                        String.valueOf(row.get("TXT_B")),
                        String.valueOf(row.get("TXT_M")),
                        String.valueOf(row.get("WGBEZ")),
                        String.valueOf(row.get("ZYLZD4")),
                        String.valueOf(row.get("PRODUCTNAME")),
                        String.valueOf(row.get("MENGE")),
                        String.valueOf(row.get("ZDD_HSDJEINDT")),
                        String.valueOf(row.get("ZHSZJ")),
                        String.valueOf(row.get("ZXMBH")),
                        String.valueOf(row.get("ZXMMC")),
                        String.valueOf(row.get("WERKS")),
                        String.valueOf(row.get("KNTTP")),
                        String.valueOf(row.get("MCSX")),
                        String.valueOf(row.get("CC_NAME")),
                        String.valueOf(row.get("WH_NAME")),
                        String.valueOf(row.get("ZPTEXT1")),
                        String.valueOf(row.get("ZPTEXT2")),
                        String.valueOf(row.get("ZPTEXT3")),
                        String.valueOf(row.get("WBSYS")),
                        String.valueOf(row.get("FBUDAT")),
                        String.valueOf(row.get("NAME_TEXTC")),
                        String.valueOf(row.get("JSZFSC")),
                        String.valueOf(row.get("HTBFQRPZH")),
                        String.valueOf(row.get("XDSHDZ")),
                        String.valueOf(row.get("SMSHRZH")),
                        String.valueOf(row.get("SMSHNAME")),
                        String.valueOf(row.get("SMSHTIME")),
                        String.valueOf(row.get("SMSHGCMC")),
                        String.valueOf(row.get("SMSHPZH")),
                        String.valueOf(row.get("SMSHDZ")),
                        String.valueOf(row.get("YCQSRZH")),
                        String.valueOf(row.get("YCQSRNAME")),
                        String.valueOf(row.get("YCQSTIME")),
                        String.valueOf(row.get("YCQSGCMC")),
                        String.valueOf(row.get("YCQSRPZH")),
                        String.valueOf(row.get("SHI1")),
                        String.valueOf(row.get("SHI2")),
                        String.valueOf(row.get("SHI3")),
                        String.valueOf(row.get("SHI4")),
                        String.valueOf(row.get("SHI5")),
                        String.valueOf(row.get("SHI6")),
                        String.valueOf(row.get("SHI7")),
                        String.valueOf(row.get("SHI8")),
                        String.valueOf(row.get("SHI9")),
                        String.valueOf(row.get("SHZSC")),
                        String.valueOf(row.get("QGFKSC")),
                        String.valueOf(row.get("ZNF")),
                        String.valueOf(row.get("SFZDWZ"))
                })
                .toArray(String[][]::new);

        return new AbnormalDetail(header, body);
    }

    /**
     * 获取结算及时性分析简要数据
     */
    private AbnormalSimplified getJSJSXSimplified(Date start, Date end) {
        String sql = "SELECT EBELN, EBELP, MCSX, CC_NAME, TXT_B, TXT_M, WGBEZ, JSZFSC " +
                "FROM SCM_LJMX_DS_ZBFX " +
                "WHERE MCSX IS NOT NULL AND CC_NAME IS NOT NULL " +
                "AND TXT_B IS NOT NULL AND TXT_M IS NOT NULL AND WGBEZ IS NOT NULL " +
                "AND JSZFSC > 60 " +
                "AND BUDAT >= ? AND BUDAT <= ? " +
                "ORDER BY BUDAT DESC";

        List<Map<String, Object>> results = hanaJdbcTemplate.queryForList(sql,
                dateFormat.format(start), dateFormat.format(end));

        String[] header = {"采购凭证号", "采购凭证行号", "项目单位名称", "供应商名称",
                "物料大类描述", "物料中类描述", "物料小类描述", "结算支付时长"};

        String[][] body = results.stream()
                .map(row -> new String[]{
                        String.valueOf(row.get("EBELN")),
                        String.valueOf(row.get("EBELP")),
                        String.valueOf(row.get("MCSX")),
                        String.valueOf(row.get("CC_NAME")),
                        String.valueOf(row.get("TXT_B")),
                        String.valueOf(row.get("TXT_M")),
                        String.valueOf(row.get("WGBEZ")),
                        String.valueOf(row.get("JSZFSC"))
                })
                .toArray(String[][]::new);

        return new AbnormalSimplified(header, body);
    }

    // ==================== 电商非电网物资-选购审批合规性 (XGSP) ====================

    /**
     * 获取选购审批合规性详细异常数据
     */
    private AbnormalDetail getXGSPDetail(Date start, Date end) {
        String sql = "SELECT EBELN, BADAT, ZHSZJ, ZXMMC, KNTTP, MCSX " +
                "FROM SCM_LJMX_DS_ZBFXJG " +
                "WHERE MCSX IS NOT NULL " +
                "AND ZHSZJ > 100000 " +
                "AND CREATE_TIME >= ? AND CREATE_TIME <= ? " +
                "ORDER BY CREATE_TIME DESC";

        List<Map<String, Object>> results = hanaJdbcTemplate.queryForList(sql,
                dateFormat.format(start), dateFormat.format(end));

        String[] header = {
                "采购凭证号（订单号）",
                "请购时间",
                "行项目总金额（含税）",
                "项目名称",
                "科目分配类别",
                "工厂名称"
        };
        String[][] body = results.stream()
                .map(row -> new String[]{
                        String.valueOf(row.get("EBELN")),
                        String.valueOf(row.get("BADAT")),
                        String.valueOf(row.get("ZHSZJ")),
                        String.valueOf(row.get("ZXMMC")),
                        String.valueOf(row.get("KNTTP")),
                        String.valueOf(row.get("MCSX"))
                })
                .toArray(String[][]::new);

        return new AbnormalDetail(header, body);
    }

    /**
     * 获取选购审批合规性简要数据
     */
    private AbnormalSimplified getXGSPSimplified(Date start, Date end) {
        String sql = "SELECT EBELN, MCSX, ZHSZJ " +
                "FROM SCM_LJMX_DS_ZBFXJG " +
                "WHERE MCSX IS NOT NULL " +
                "AND ZHSZJ > 100000 " +
                "AND CREATE_TIME >= ? AND CREATE_TIME <= ? " +
                "ORDER BY CREATE_TIME DESC";

        List<Map<String, Object>> results = hanaJdbcTemplate.queryForList(sql,
                dateFormat.format(start), dateFormat.format(end));

        String[] header = {"采购凭证号", "项目单位", "订单总金额"};

        String[][] body = results.stream()
                .map(row -> new String[]{
                        String.valueOf(row.get("EBELN")),
                        String.valueOf(row.get("MCSX")),
                        String.valueOf(row.get("ZHSZJ"))
                })
                .toArray(String[][]::new);

        return new AbnormalSimplified(header, body);
    }

    // ==================== 电商非电网物资-物资积压率 (WZJY) ====================

    /**
     * 获取物资积压率详细异常数据
     */
    private AbnormalDetail getWZJYDetail(Date start, Date end) {
        String sql = "SELECT EBELN, EBELP, WERKS, BWART, ELIKZ, MATNR, ZGZSJ, USNAM, " +
                "WQSHSJ, ZYLZD4, PRODUCTNAME, SFRK, SFTB, WH_NO, WH_NAME, FIRM_NAME, " +
                "SIGN_NAME_RUKU, SIGN_TIME, VENDOR, MATERIAL_CATEGORY_NAME, " +
                "MATERIAL_MIDDLE_NAME, MATERIAL_SMALL_NAME, OVER_NUM_RUKU, " +
                "SCAN_MATERIAL_NUM, CREATE_TIME, LABEL_CODE, SIGN_NAME_CHUKU, " +
                "CONNECT_NAME, CONNECT_TIME, OVER_NUM_CHUKU, OVER_TIME, XDSHDZ, " +
                "SMSHNAME, SMSHTIME, SMSHDZ, YCQSRZH, YCQSRNAME, YCQSTIME, DWELL_TIME " +
                "FROM SCM_LJMX_DS_DTLY " +
                "WHERE FIRM_NAME IS NOT NULL " +
                "AND DWELL_TIME > 864000 " +
                "AND CREATE_TIME >= ? AND CREATE_TIME <= ? " +
                "ORDER BY CREATE_TIME DESC";

        List<Map<String, Object>> results = hanaJdbcTemplate.queryForList(sql,
                dateFormat.format(start), dateFormat.format(end));

        String[] header = {
                "采购凭证号", "采购凭证行号", "工厂编码", "移动类型",
                "交货已完成", "物料", "过账时间", "ERP收货人账号",
                "完全收货时间", "商品ID", "商品名称", "是否入库",
                "是否贴标", "仓库编码", "仓库名称", "项目单位名称",
                "仓库签收人", "签收日期", "供应商", "物料大类名称",
                "物料中类名称", "物料小类名称", "已入库数量", "已扫描数量",
                "入库操作时间", "是否扫码入库", "出库操作人", "领料人",
                "领料日期", "已出库数量", "出库操作时间", "下单收货地址",
                "扫码收货姓名", "扫码收货时间", "扫码收货地址", "远程签收人账号",
                "远程签收人姓名", "远程签收时间", "在库时长"
        };

        String[][] body = results.stream()
                .map(row -> new String[]{
                        String.valueOf(row.get("EBELN")),
                        String.valueOf(row.get("EBELP")),
                        String.valueOf(row.get("WERKS")),
                        String.valueOf(row.get("BWART")),
                        String.valueOf(row.get("ELIKZ")),
                        String.valueOf(row.get("MATNR")),
                        String.valueOf(row.get("ZGZSJ")),
                        String.valueOf(row.get("USNAM")),
                        String.valueOf(row.get("WQSHSJ")),
                        String.valueOf(row.get("ZYLZD4")),
                        String.valueOf(row.get("PRODUCTNAME")),
                        String.valueOf(row.get("SFRK")),
                        String.valueOf(row.get("SFTB")),
                        String.valueOf(row.get("WH_NO")),
                        String.valueOf(row.get("WH_NAME")),
                        String.valueOf(row.get("FIRM_NAME")),
                        String.valueOf(row.get("SIGN_NAME_RUKU")),
                        String.valueOf(row.get("SIGN_TIME")),
                        String.valueOf(row.get("VENDOR")),
                        String.valueOf(row.get("MATERIAL_CATEGORY_NAME")),
                        String.valueOf(row.get("MATERIAL_MIDDLE_NAME")),
                        String.valueOf(row.get("MATERIAL_SMALL_NAME")),
                        String.valueOf(row.get("OVER_NUM_RUKU")),
                        String.valueOf(row.get("SCAN_MATERIAL_NUM")),
                        String.valueOf(row.get("CREATE_TIME")),
                        String.valueOf(row.get("LABEL_CODE")),
                        String.valueOf(row.get("SIGN_NAME_CHUKU")),
                        String.valueOf(row.get("CONNECT_NAME")),
                        String.valueOf(row.get("CONNECT_TIME")),
                        String.valueOf(row.get("OVER_NUM_CHUKU")),
                        String.valueOf(row.get("OVER_TIME")),
                        String.valueOf(row.get("XDSHDZ")),
                        String.valueOf(row.get("SMSHNAME")),
                        String.valueOf(row.get("SMSHTIME")),
                        String.valueOf(row.get("SMSHDZ")),
                        String.valueOf(row.get("YCQSRZH")),
                        String.valueOf(row.get("YCQSRNAME")),
                        String.valueOf(row.get("YCQSTIME")),
                        String.valueOf(row.get("DWELL_TIME"))
                })
                .toArray(String[][]::new);

        return new AbnormalDetail(header, body);
    }

    /**
     * 获取物资积压率简要数据
     */
    private AbnormalSimplified getWZJYSimplified(Date start, Date end) {
        String sql = "SELECT PRODUCTNAME, DWELL_TIME, MATERIAL_CATEGORY_NAME, " +
                "MATERIAL_MIDDLE_NAME, MATERIAL_SMALL_NAME, WH_NAME, FIRM_NAME " +
                "FROM SCM_LJMX_DS_DTLY " +
                "WHERE FIRM_NAME IS NOT NULL " +
                "AND DWELL_TIME > 864000 " +
                "AND CREATE_TIME >= ? AND CREATE_TIME <= ? " +
                "ORDER BY CREATE_TIME DESC";

        List<Map<String, Object>> results = hanaJdbcTemplate.queryForList(sql,
                dateFormat.format(start), dateFormat.format(end));

        String[] header = {"商品名称", "在库时长", "物料大类", "物料中类",
                "物料小类", "仓库", "项目单位"};

        String[][] body = results.stream()
                .map(row -> new String[]{
                        String.valueOf(row.get("PRODUCTNAME")),
                        String.valueOf(row.get("DWELL_TIME")),
                        String.valueOf(row.get("MATERIAL_CATEGORY_NAME")),
                        String.valueOf(row.get("MATERIAL_MIDDLE_NAME")),
                        String.valueOf(row.get("MATERIAL_SMALL_NAME")),
                        String.valueOf(row.get("WH_NAME")),
                        String.valueOf(row.get("FIRM_NAME"))
                })
                .toArray(String[][]::new);

        return new AbnormalSimplified(header, body);
    }

    // ==================== 电商非电网物资-帐号权限分离合规性 (ZHQXFL) ====================

    /**
     * 获取账号权限分离合规性详细异常数据
     */
    private AbnormalDetail getZHQXFLDetail(Date start, Date end) {
        String sql = "SELECT EBELN, EBELP, ZQGDH, ZGGDHXM, ERNAM, AFNAM, QGSQRPZH, BADAT, " +
                "USERNAME_XQSHR, XQSHRXM, XQSHRPZH, UDATE_XQSHR, UTIME_XQSHR, USERNAME_XQSPR, " +
                "XQSPRXM, XQSPRPZH, UDATE_XQSPR, UTIME_XQSPR, USERNAME_DDSPR, DDSPRXM, DDSPRPZH, " +
                "UDATE_DDSPR, UTIME_DDSPR, USNAM, XTSHRXM, XTSHRPZH, BUDAT, MATNR, ZJSCS, ZKZBM, " +
                "TXT_B, TXT_M, WGBEZ, ZYLZD4, PRODUCTNAME, MENGE, ZDD_HSDJEINDT, ZHSZJ, ZXMBH, " +
                "ZXMMC, WERKS, KNTTP, MCSX, CC_NAME, WH_NAME, ZPTEXT1, ZPTEXT2, ZPTEXT3, WBSYS, " +
                "FBUDAT, NAME_TEXTC, JSZFSC, HTBFQRPZH, XDSHDZ, SMSHRZH, SMSHNAME, SMSHTIME, " +
                "SMSHGCMC, SMSHPZH, SMSHDZ, YCQSRZH, YCQSRNAME, YCQSTIME, YCQSGCMC, YCQSRPZH, " +
                "SHI1, SHI2, SHI3, SHI4, SHI5, SHI6, SHI7, SHI8, SHI9, SHZSC, QGFKSC, ZNF, SFZDWZ " +
                "FROM SCM_LJMX_DS_ZBFX " +
                "WHERE BUDAT >= ? AND BUDAT <= ? " +
                "ORDER BY BUDAT DESC";

        List<Map<String, Object>> results = hanaJdbcTemplate.queryForList(sql,
                dateFormat.format(start), dateFormat.format(end));

        String[] header = {
                "采购凭证号", "采购凭证行号", "请购单号", "请购单行号",
                "请购人姓名", "请购申请人账号", "请购申请人P账号", "请购时间",
                "需求审核人账号", "需求审核人姓名", "需求审核人P账号", "需求审核日期",
                "需求审核时间", "需求审批人账号", "需求审批人姓名", "需求审批人P账号",
                "需求审批人日期", "需求审批人时间", "订单审批人账号", "订单审批人姓名",
                "订单审批人P账号", "订单审批日期", "订单审批时间", "ERP系统收货人账号",
                "ERP系统收货人姓名", "ERP系统收货人P账号", "ERP系统收货时间", "物料编号",
                "物料扩展描述", "物料扩展编码", "物料大类描述", "物料中类描述",
                "物料小类描述", "商品编码", "商品名称", "购买数量",
                "单价（含税）", "行项目总金额（含税）", "项目编号", "项目名称",
                "工厂编码", "科目分配类别", "工厂名称", "供应商名称",
                "仓库名称", "项目大类", "项目中类", "项目小类",
                "WBS元素", "合同部发起支付时间", "合同部发起申请人", "结算支付时长",
                "合同部发起人P账号", "下单收货地址", "扫码收货人账号", "扫码收货姓名",
                "扫码收货时间", "扫码收货人所在工厂", "扫码收货人P账号", "扫码收货地址",
                "远程签收人账号", "远程签收人姓名", "远程签收时间", "远程签收人所属工厂",
                "远程签收人P账号", "请购申请人-需求审核人", "请购申请人-需求审批人", "请购申请人-订单审批人",
                "请购申请人-扫码收货人", "请购申请人-远程签收人", "请购申请人-系统收货人", "需求审核人-需求审批人",
                "需求审批人-订单审批人", "需求审核人-订单审批人", "收货总时长", "请购付款时长",
                "总年份", "是否重点物资"
        };

        String[][] body = results.stream()
                .map(row -> new String[]{
                        String.valueOf(row.get("EBELN")),
                        String.valueOf(row.get("EBELP")),
                        String.valueOf(row.get("ZQGDH")),
                        String.valueOf(row.get("ZGGDHXM")),
                        String.valueOf(row.get("ERNAM")),
                        String.valueOf(row.get("AFNAM")),
                        String.valueOf(row.get("QGSQRPZH")),
                        String.valueOf(row.get("BADAT")),
                        String.valueOf(row.get("USERNAME_XQSHR")),
                        String.valueOf(row.get("XQSHRXM")),
                        String.valueOf(row.get("XQSHRPZH")),
                        String.valueOf(row.get("UDATE_XQSHR")),
                        String.valueOf(row.get("UTIME_XQSHR")),
                        String.valueOf(row.get("USERNAME_XQSPR")),
                        String.valueOf(row.get("XQSPRXM")),
                        String.valueOf(row.get("XQSPRPZH")),
                        String.valueOf(row.get("UDATE_XQSPR")),
                        String.valueOf(row.get("UTIME_XQSPR")),
                        String.valueOf(row.get("USERNAME_DDSPR")),
                        String.valueOf(row.get("DDSPRXM")),
                        String.valueOf(row.get("DDSPRPZH")),
                        String.valueOf(row.get("UDATE_DDSPR")),
                        String.valueOf(row.get("UTIME_DDSPR")),
                        String.valueOf(row.get("USNAM")),
                        String.valueOf(row.get("XTSHRXM")),
                        String.valueOf(row.get("XTSHRPZH")),
                        String.valueOf(row.get("BUDAT")),
                        String.valueOf(row.get("MATNR")),
                        String.valueOf(row.get("ZJSCS")),
                        String.valueOf(row.get("ZKZBM")),
                        String.valueOf(row.get("TXT_B")),
                        String.valueOf(row.get("TXT_M")),
                        String.valueOf(row.get("WGBEZ")),
                        String.valueOf(row.get("ZYLZD4")),
                        String.valueOf(row.get("PRODUCTNAME")),
                        String.valueOf(row.get("MENGE")),
                        String.valueOf(row.get("ZDD_HSDJEINDT")),
                        String.valueOf(row.get("ZHSZJ")),
                        String.valueOf(row.get("ZXMBH")),
                        String.valueOf(row.get("ZXMMC")),
                        String.valueOf(row.get("WERKS")),
                        String.valueOf(row.get("KNTTP")),
                        String.valueOf(row.get("MCSX")),
                        String.valueOf(row.get("CC_NAME")),
                        String.valueOf(row.get("WH_NAME")),
                        String.valueOf(row.get("ZPTEXT1")),
                        String.valueOf(row.get("ZPTEXT2")),
                        String.valueOf(row.get("ZPTEXT3")),
                        String.valueOf(row.get("WBSYS")),
                        String.valueOf(row.get("FBUDAT")),
                        String.valueOf(row.get("NAME_TEXTC")),
                        String.valueOf(row.get("JSZFSC")),
                        String.valueOf(row.get("HTBFQRPZH")),
                        String.valueOf(row.get("XDSHDZ")),
                        String.valueOf(row.get("SMSHRZH")),
                        String.valueOf(row.get("SMSHNAME")),
                        String.valueOf(row.get("SMSHTIME")),
                        String.valueOf(row.get("SMSHGCMC")),
                        String.valueOf(row.get("SMSHPZH")),
                        String.valueOf(row.get("SMSHDZ")),
                        String.valueOf(row.get("YCQSRZH")),
                        String.valueOf(row.get("YCQSRNAME")),
                        String.valueOf(row.get("YCQSTIME")),
                        String.valueOf(row.get("YCQSGCMC")),
                        String.valueOf(row.get("YCQSRPZH")),
                        String.valueOf(row.get("SHI1")),
                        String.valueOf(row.get("SHI2")),
                        String.valueOf(row.get("SHI3")),
                        String.valueOf(row.get("SHI4")),
                        String.valueOf(row.get("SHI5")),
                        String.valueOf(row.get("SHI6")),
                        String.valueOf(row.get("SHI7")),
                        String.valueOf(row.get("SHI8")),
                        String.valueOf(row.get("SHI9")),
                        String.valueOf(row.get("SHZSC")),
                        String.valueOf(row.get("QGFKSC")),
                        String.valueOf(row.get("ZNF")),
                        String.valueOf(row.get("SFZDWZ"))
                })
                .toArray(String[][]::new);

        return new AbnormalDetail(header, body);
    }

    /**
     * 获取账号权限分离合规性简要数据
     */
    private AbnormalSimplified getZHQXFLSimplified(Date start, Date end) {
        String sql = "SELECT MCSX, EBELN, EBELP " +
                "FROM SCM_LJMX_DS_ZBFX " +
                "WHERE  BUDAT >= ? AND BUDAT <= ? " +
                "ORDER BY BUDAT DESC";

        List<Map<String, Object>> results = hanaJdbcTemplate.queryForList(sql,
                dateFormat.format(start), dateFormat.format(end));

        String[] header = {"项目单位", "采购凭证号", "采购凭证行号"};

        String[][] body = results.stream()
                .map(row -> new String[]{
                        String.valueOf(row.get("MCSX")),
                        String.valueOf(row.get("EBELN")),
                        String.valueOf(row.get("EBELP"))
                })
                .toArray(String[][]::new);

        return new AbnormalSimplified(header, body);
    }

    // ==================== 电商非电网物资-当天领用出库率 (DTLY) ====================

    /**
     * 获取当天领用出库率详细异常数据
     */
    private AbnormalDetail getDTLYDetail(Date start, Date end) {
        String sql = "SELECT EBELN, EBELP, WERKS, BWART, ELIKZ, MATNR, ZGZSJ, USNAM, " +
                "WQSHSJ, ZYLZD4, PRODUCTNAME, SFRK, SFTB, WH_NO, WH_NAME, FIRM_NAME, " +
                "SIGN_NAME_RUKU, SIGN_TIME, VENDOR, MATERIAL_CATEGORY_NAME, " +
                "MATERIAL_MIDDLE_NAME, MATERIAL_SMALL_NAME, OVER_NUM_RUKU, " +
                "SCAN_MATERIAL_NUM, CREATE_TIME, LABEL_CODE, SIGN_NAME_CHUKU, " +
                "CONNECT_NAME, CONNECT_TIME, OVER_NUM_CHUKU, OVER_TIME, XDSHDZ, " +
                "SMSHNAME, SMSHTIME, SMSHDZ, YCQSRZH, YCQSRNAME, YCQSTIME, DWELL_TIME " +
                "FROM SCM_LJMX_DS_DTLY " +
                "WHERE FIRM_NAME IS NOT NULL " +
                "AND DWELL_TIME <= 86400 " +
                "AND CREATE_TIME >= ? AND CREATE_TIME <= ? " +
                "ORDER BY CREATE_TIME DESC";

        List<Map<String, Object>> results = hanaJdbcTemplate.queryForList(sql,
                dateFormat.format(start), dateFormat.format(end));

        String[] header = {
                "采购凭证号", "采购凭证行号", "工厂编码", "移动类型",
                "交货已完成", "物料", "过账时间", "ERP收货人账号",
                "完全收货时间", "商品ID", "商品名称", "是否入库",
                "是否贴标", "仓库编码", "仓库名称", "项目单位名称",
                "仓库签收人", "签收日期", "供应商", "物料大类名称",
                "物料中类名称", "物料小类名称", "已入库数量", "已扫描数量",
                "入库操作时间", "是否扫码入库", "出库操作人", "领料人",
                "领料日期", "已出库数量", "出库操作时间", "下单收货地址",
                "扫码收货姓名", "扫码收货时间", "扫码收货地址", "远程签收人账号",
                "远程签收人姓名", "远程签收时间", "在库时长"
        };

        String[][] body = results.stream()
                .map(row -> new String[]{
                        String.valueOf(row.get("EBELN")),
                        String.valueOf(row.get("EBELP")),
                        String.valueOf(row.get("WERKS")),
                        String.valueOf(row.get("BWART")),
                        String.valueOf(row.get("ELIKZ")),
                        String.valueOf(row.get("MATNR")),
                        String.valueOf(row.get("ZGZSJ")),
                        String.valueOf(row.get("USNAM")),
                        String.valueOf(row.get("WQSHSJ")),
                        String.valueOf(row.get("ZYLZD4")),
                        String.valueOf(row.get("PRODUCTNAME")),
                        String.valueOf(row.get("SFRK")),
                        String.valueOf(row.get("SFTB")),
                        String.valueOf(row.get("WH_NO")),
                        String.valueOf(row.get("WH_NAME")),
                        String.valueOf(row.get("FIRM_NAME")),
                        String.valueOf(row.get("SIGN_NAME_RUKU")),
                        String.valueOf(row.get("SIGN_TIME")),
                        String.valueOf(row.get("VENDOR")),
                        String.valueOf(row.get("MATERIAL_CATEGORY_NAME")),
                        String.valueOf(row.get("MATERIAL_MIDDLE_NAME")),
                        String.valueOf(row.get("MATERIAL_SMALL_NAME")),
                        String.valueOf(row.get("OVER_NUM_RUKU")),
                        String.valueOf(row.get("SCAN_MATERIAL_NUM")),
                        String.valueOf(row.get("CREATE_TIME")),
                        String.valueOf(row.get("LABEL_CODE")),
                        String.valueOf(row.get("SIGN_NAME_CHUKU")),
                        String.valueOf(row.get("CONNECT_NAME")),
                        String.valueOf(row.get("CONNECT_TIME")),
                        String.valueOf(row.get("OVER_NUM_CHUKU")),
                        String.valueOf(row.get("OVER_TIME")),
                        String.valueOf(row.get("XDSHDZ")),
                        String.valueOf(row.get("SMSHNAME")),
                        String.valueOf(row.get("SMSHTIME")),
                        String.valueOf(row.get("SMSHDZ")),
                        String.valueOf(row.get("YCQSRZH")),
                        String.valueOf(row.get("YCQSRNAME")),
                        String.valueOf(row.get("YCQSTIME")),
                        String.valueOf(row.get("DWELL_TIME"))
                })
                .toArray(String[][]::new);

        return new AbnormalDetail(header, body);
    }

    /**
     * 获取当天领用出库率简要数据
     */
    private AbnormalSimplified getDTLYSimplified(Date start, Date end) {
        String sql = "SELECT PRODUCTNAME, DWELL_TIME, MATERIAL_CATEGORY_NAME, " +
                "MATERIAL_MIDDLE_NAME, MATERIAL_SMALL_NAME, WH_NAME, FIRM_NAME " +
                "FROM SCM_LJMX_DS_DTLY " +
                "WHERE FIRM_NAME IS NOT NULL " +
                "AND DWELL_TIME <= 86400 " +
                "AND CREATE_TIME >= ? AND CREATE_TIME <= ? " +
                "ORDER BY CREATE_TIME DESC";

        List<Map<String, Object>> results = hanaJdbcTemplate.queryForList(sql,
                dateFormat.format(start), dateFormat.format(end));

        String[] header = {"商品名称", "在库时长", "物料大类", "物料中类",
                "物料小类", "仓库", "项目单位"};

        String[][] body = results.stream()
                .map(row -> new String[]{
                        String.valueOf(row.get("PRODUCTNAME")),
                        String.valueOf(row.get("DWELL_TIME")),
                        String.valueOf(row.get("MATERIAL_CATEGORY_NAME")),
                        String.valueOf(row.get("MATERIAL_MIDDLE_NAME")),
                        String.valueOf(row.get("MATERIAL_SMALL_NAME")),
                        String.valueOf(row.get("WH_NAME")),
                        String.valueOf(row.get("FIRM_NAME"))
                })
                .toArray(String[][]::new);

        return new AbnormalSimplified(header, body);
    }


    // 请购频次详细数据
    public List<Map<String, Object>> getQGPCDetails(Date start, Date end) {
        String sql = "WITH hist_data AS (" +
                "    SELECT " +
                "        TXT_B, TXT_M, WGBEZ," +
                "        COUNT(*) AS hist_total_freq," +
                "        COUNT(DISTINCT EXTRACT(YEAR FROM BADAT)) AS hist_years" +
                "    FROM SCM_LJMX_DS_ZBFX" +
                "    WHERE EXTRACT(YEAR FROM BADAT) < EXTRACT(YEAR FROM CURRENT_DATE)" +
                "    GROUP BY TXT_B, TXT_M, WGBEZ" +
                ")," +
                "current_data AS (" +
                "    SELECT " +
                "        ZQGDH," +
                "        ZGGDHXM," +
                "        MCSX," +
                "极       TXT_B," +
                "        TXT_M," +
                "        WGBEZ," +
                "        COUNT(*) OVER(PARTITION BY TXT极_B, TXT_M, WGBEZ) AS current_freq" +
                "    FROM SCM_LJMX_DS_ZBFX" +
                "    WHERE MCSX IS NOT NULL" +
                "      AND TXT_B IS NOT NULL" +
                "      AND TXT_M IS NOT NULL" +
                "      AND WGBEZ IS NOT NULL" +
                "      AND PRODUCTNAME IS NOT NULL" +
                "      AND BADAT BETWEEN ? AND ?" +
                ")" +
                "SELECT DISTINCT" +
                "    c.ZQGDH," +
                "    c.ZGGDHXM," +
                "    c.MCSX," +
                "    c.TXT_B," +
                "    c.TXT_M," +
                "    c.WGBEZ," +
                "    c.current_freq," +
                "    COALESCE(h.hist_total_freq/NULLIF(h.hist_years, 0), 0)" +
                "FROM current_data c" +
                "LEFT JOIN hist_data h " +
                "    ON c.TXT_B = h.TXT_B" +
                "   AND c.TXT_M = h.TXT_M" +
                "   AND c.WGBEZ = h.WGBEZ";

        return hanaJdbcTemplate.queryForList(sql,
                new java.sql.Date(start.getTime()),
                new java.sql.Date(end.getTime()));
    }


}
