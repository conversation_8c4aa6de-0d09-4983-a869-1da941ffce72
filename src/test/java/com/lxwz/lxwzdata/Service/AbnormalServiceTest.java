package com.lxwz.lxwzdata.Service;

import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit.jupiter.SpringJUnitConfig;

import java.util.Date;
import java.util.Calendar;

import static org.junit.jupiter.api.Assertions.*;

/**
 * AbnormalService 测试类
 */
@SpringBootTest
@SpringJUnitConfig
public class AbnormalServiceTest {

    private AbnormalService abnormalService = new AbnormalService();

    @Test
    public void testGetDetailWithValidZbID() {
        // 准备测试数据
        Calendar cal = Calendar.getInstance();
        Date endTime = cal.getTime();
        cal.add(Calendar.DAY_OF_MONTH, -30);
        Date startTime = cal.getTime();

        // 测试各个指标
        String[] zbIDs = {"BQBD", "WZRKL", "SMRK", "JSJ<PERSON><PERSON>", "XGSP", "WZJY", "ZHQXFL", "DTLY"};
        
        for (String zbID : zbIDs) {
            AbnormalService.AbnormalDetail detail = abnormalService.getDetail(startTime, endTime, zbID);
            assertNotNull(detail, "Detail should not be null for zbID: " + zbID);
            assertNotNull(detail.header, "Header should not be null for zbID: " + zbID);
            assertNotNull(detail.body, "Body should not be null for zbID: " + zbID);
        }
    }

    @Test
    public void testGetSimplifiedWithValidZbID() {
        // 准备测试数据
        Calendar cal = Calendar.getInstance();
        Date endTime = cal.getTime();
        cal.add(Calendar.DAY_OF_MONTH, -30);
        Date startTime = cal.getTime();

        // 测试各个指标
        String[] zbIDs = {"BQBD", "WZRKL", "SMRK", "JSJSX", "XGSP", "WZJY", "ZHQXFL", "DTLY"};
        
        for (String zbID : zbIDs) {
            AbnormalService.AbnormalSimplified simplified = abnormalService.getSimplified(startTime, endTime, zbID);
            assertNotNull(simplified, "Simplified should not be null for zbID: " + zbID);
            assertNotNull(simplified.header, "Header should not be null for zbID: " + zbID);
            assertNotNull(simplified.body, "Body should not be null for zbID: " + zbID);
        }
    }

    @Test
    public void testGetDetailWithInvalidZbID() {
        Calendar cal = Calendar.getInstance();
        Date endTime = cal.getTime();
        cal.add(Calendar.DAY_OF_MONTH, -30);
        Date startTime = cal.getTime();

        AbnormalService.AbnormalDetail detail = abnormalService.getDetail(startTime, endTime, "INVALID");
        assertNotNull(detail, "Detail should not be null even for invalid zbID");
    }

    @Test
    public void testGetSimplifiedWithInvalidZbID() {
        Calendar cal = Calendar.getInstance();
        Date endTime = cal.getTime();
        cal.add(Calendar.DAY_OF_MONTH, -30);
        Date startTime = cal.getTime();

        AbnormalService.AbnormalSimplified simplified = abnormalService.getSimplified(startTime, endTime, "INVALID");
        assertNotNull(simplified, "Simplified should not be null even for invalid zbID");
    }
}
